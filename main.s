;---------------------------------------
; Verge
;
; by <PERSON><PERSON><PERSON> (Wisdom)
;
; Copyright (C) 2025 <PERSON><PERSON><PERSON>
;---------------------------------------
ia = $02
ix = $03
iy = $04
na = $05
nx = $06
ny = $07
cram = $d800
vram1 = $4000
vram2 = $4400
sprites = $4800
charset1 = $6000
charset2 = $6800
charset3 = $7000
charset4 = $7800
;---------------------------------------
            *= $0801
            .word $080d
            .word 2025
            .byte $9e
            .text "2064"
            .byte $57,$44,$4d
            .byte 0,0,0
;---------------------------------------
main
            sei
            lda #$35
            sta $01
            lda #$7f
            sta $dc0d
            lda #$01
            sta $d01a
            lda #$1b
            sta $d011
            lda #$32
            sta $d012
            lda #<nmi
            sta $fffa
            lda #>nmi
            sta $fffb
            lda #<irq
            sta $fffe
            lda #>irq
            sta $ffff
            cli
            jmp *
;---------------------------------------
nmi
            rti
;---------------------------------------
irq
            sta ia
            stx ix
            sty iy

            dec $d020
            ldx #$08
            dex
            bne *-1
            inc $d020

            inc $d019
            ; lda $dc0d

            lda ia
            ldx ix
            ldy iy
            rti
;---------------------------------------
vsync
            lda $d011
            bpl *-3
            lda $d011
            bmi *-3
            rts
;---------------------------------------
.virtual
    .namespace level1_layer1
        .include "level01_layer1.asm" 
    .endnamespace
    
    .namespace level1_layer2
        .include "level01_layer2_frame1.asm" 
    .endnamespace
.endvirtual
;---------------------------------------
            .align $0100
level1_charset_colors
.for i = 0, i < sizeof(level1_layer1.charset_attrib_data), i = i + 1
    .byte level1_layer1.charset_attrib_data[i]
.next
.for i = 0, i < sizeof(level1_layer2.charset_attrib_data), i = i + 1
    .byte level1_layer2.charset_attrib_data[i]
.next
;---------------------------------------
            .align $0100
level1_layer1_map
.for i = 0, i < sizeof(level1_layer1.map_data), i = i + 1
    .byte level1_layer1.map_data[i]
.next
;---------------------------------------
            * = charset1
.for i = 0, i < sizeof(level1_layer1.charset_data), i = i + 1
    .byte level1_layer1.charset_data[i]
.next
.for i = 0, i < sizeof(level1_layer2_frame1.charset_data), i = i + 1
    .byte level1_layer2_frame1.charset_data[i]
.next
;---------------------------------------
