
; Generated by CharPad 2.8, Subchrist Software, 2021.
; Assemble with 64TASS or similar.


; Character display mode : Multi-colour.

; Character colouring method : Per-Character.


; Colour values...

COLR_SCREEN = 14
COLR_CHAR_DEF = 8
COLR_CHAR_MC1 = 9
COLR_CHAR_MC2 = 10


; Quantities and dimensions...

CHAR_COUNT = 77
MAP_WID = 220
MAP_HEI = 16


; Data block sizes (in bytes)...

SZ_CHARSET_DATA        = 616 ; ($268)
SZ_CHARSET_ATTRIB_DATA = 77 ; ($4D)
SZ_MAP_DATA            = 3520 ; ($DC0)




; * INSERT EXAMPLE PROGRAM HERE! * (Or just include this file in your project).




; CHARSET IMAGE DATA...
; 77 images, 8 bytes per image, total size is 616 ($268) bytes.

charset_data

.byte $00,$00,$00,$00,$00,$00,$00,$00,$7F,$1F,$0F,$07,$07,$03,$01,$01
.byte $75,$DD,$F5,$DD,$F5,$FD,$F7,$FD,$9A,$5A,$96,$65,$95,$69,$56,$66
.byte $EE,$E9,$A4,$54,$50,$90,$80,$C0,$00,$00,$00,$00,$00,$01,$03,$03
.byte $00,$00,$01,$15,$56,$59,$56,$59,$00,$00,$64,$AA,$BB,$AF,$6B,$AA
.byte $00,$00,$00,$00,$40,$80,$D0,$E0,$07,$07,$0F,$0D,$0F,$1F,$1D,$1F
.byte $D5,$F5,$75,$F5,$DD,$75,$D5,$F5,$66,$9A,$69,$5A,$66,$9A,$66,$9A
.byte $D0,$F0,$E0,$B4,$E4,$B8,$E4,$B8,$1F,$1F,$3F,$3D,$35,$5D,$77,$7F
.byte $F5,$7D,$F5,$55,$D5,$5D,$D5,$DD,$69,$66,$95,$66,$95,$55,$5A,$66
.byte $B4,$BA,$A6,$BA,$EE,$B9,$FE,$B9,$01,$01,$03,$07,$07,$0F,$1F,$7F
.byte $F5,$DD,$F5,$DD,$75,$D7,$77,$DF,$9A,$AA,$9A,$96,$5A,$56,$5A,$66
.byte $90,$E0,$E0,$90,$90,$E0,$E0,$B9,$D9,$D9,$D9,$D9,$D9,$D9,$D9,$D9
.byte $77,$7F,$77,$5F,$77,$D7,$5F,$7F,$D5,$F5,$FF,$75,$D5,$75,$D5,$56
.byte $9A,$66,$5A,$5A,$96,$66,$AA,$66,$F9,$EE,$F9,$B9,$FE,$B9,$FE,$B9
.byte $00,$00,$00,$00,$01,$03,$1F,$FF,$0F,$15,$17,$5F,$FD,$F5,$F5,$D7
.byte $DD,$7F,$5D,$5D,$7D,$F6,$D5,$79,$56,$59,$5A,$67,$A7,$97,$7D,$DD
.byte $56,$5A,$56,$5A,$66,$5A,$66,$5A,$BF,$EF,$BB,$EF,$BE,$AF,$BB,$AF
.byte $A4,$E4,$A8,$E9,$BA,$EA,$FB,$EE,$00,$00,$00,$00,$00,$40,$90,$E9
.byte $D9,$5F,$1D,$07,$03,$01,$01,$01,$6A,$D5,$F7,$FF,$77,$5F,$7D,$FF
.byte $9A,$55,$7F,$DD,$F5,$75,$DD,$55,$AA,$59,$55,$65,$95,$59,$96,$5A
.byte $EF,$AA,$59,$55,$6A,$AF,$BE,$AF,$FE,$E9,$54,$A0,$C0,$80,$80,$40
.byte $01,$01,$03,$03,$07,$07,$0F,$0F,$FD,$F7,$FD,$F7,$D5,$7D,$FF,$FF
.byte $D5,$56,$D5,$56,$D5,$56,$59,$56,$A9,$65,$96,$65,$96,$65,$9A,$96
.byte $66,$FB,$BF,$AE,$BF,$AF,$BE,$FF,$40,$80,$A0,$D0,$A0,$90,$E0,$90
.byte $55,$AA,$AA,$AA,$55,$AA,$AA,$55,$5F,$55,$AA,$AA,$55,$AA,$AA,$55
.byte $F7,$59,$97,$AA,$55,$AA,$AA,$55,$F5,$D9,$75,$95,$55,$AA,$AA,$55
.byte $66,$9A,$6A,$59,$55,$AA,$AA,$55,$BA,$EF,$B6,$6A,$55,$AA,$AA,$55
.byte $FD,$66,$AA,$AA,$55,$AA,$AA,$55,$D9,$D9,$D9,$D9,$D9,$D9,$96,$55
.byte $55,$AA,$AA,$AA,$55,$AA,$AA,$55,$77,$95,$AA,$AA,$55,$AA,$AA,$55
.byte $77,$DF,$5D,$A5,$55,$AA,$AA,$55,$5A,$66,$6A,$A6,$55,$AA,$AA,$55
.byte $ED,$A6,$6A,$AA,$55,$AA,$AA,$55,$1D,$59,$DA,$56,$1D,$07,$00,$00
.byte $1F,$F5,$5A,$A9,$57,$DF,$7D,$00,$D9,$57,$A5,$5A,$76,$DD,$77,$FB
.byte $1F,$F5,$5A,$A9,$97,$5D,$D0,$40,$F4,$5D,$A9,$65,$D7,$7C,$00,$00
.byte $FE,$EA,$55,$A0,$C0,$80,$80,$40,$BC,$6B,$56,$05,$00,$00,$00,$00
.byte $00,$E8,$6E,$5B,$15,$01,$00,$00,$00,$00,$56,$A9,$66,$55,$58,$18
.byte $10,$76,$AD,$5F,$FD,$10,$00,$00,$01,$97,$5F,$FD,$40,$00,$00,$00
.byte $D9,$FF,$5D,$1F,$07,$01,$01,$01,$5F,$D5,$6A,$95,$5F,$75,$C0,$00
.byte $D5,$5A,$A9,$57,$FD,$D0,$00,$00,$40,$97,$65,$56,$75,$1F,$05,$00
.byte $74,$D5,$69,$96,$5D,$F7,$41,$00,$1F,$D5,$66,$AA,$55,$7D,$D3,$00
.byte $AA,$55,$55,$AA,$55,$55,$55,$55


; CHARSET IMAGE ATTRIBUTE DATA...
; 77 attributes, 1 attribute per image, 8 bits per attribute, total size is 77 ($4D) bytes.
; nb. Upper nybbles = material, lower nybbles = colour.

charset_attrib_data

.byte $80,$88,$88,$88,$8F,$88,$88,$8F,$8F,$88,$88,$88,$8F,$88,$88,$88
.byte $8F,$88,$88,$88,$8F,$88,$88,$88,$88,$8F,$88,$88,$88,$88,$88,$8F
.byte $8F,$8F,$88,$88,$88,$88,$8F,$8F,$88,$88,$88,$88,$8F,$8F,$8F,$88
.byte $88,$88,$88,$8F,$8F,$88,$88,$88,$88,$88,$8F,$88,$88,$88,$88,$88
.byte $8F,$8F,$8F,$8F,$88,$88,$88,$88,$88,$88,$88,$88,$88


; MAP DATA...
; 220x16 (3520) cells, 8 bits per cell, total size is 3520 ($DC0) bytes.

map_data

.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$3B,$3C,$3D,$48,$49,$4A,$4B
.byte $47,$48,$49,$4A,$4B,$47,$3D,$48,$49,$4A,$4B,$47,$48,$49,$4A,$4B
.byte $47,$3D,$3E,$3F,$00,$00,$00,$00,$00,$00,$00,$00,$00,$05,$06,$07
.byte $08,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$05,$06,$07,$08,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$05,$06,$07,$08,$00,$00
.byte $00,$00,$00,$00,$00,$22,$23,$24,$25,$26,$40,$41,$42,$43,$44,$45
.byte $46,$23,$24,$25,$26,$27,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$05,$06,$07,$08,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$05,$06,$07,$08,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$15,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$15,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$09,$0A,$0B,$0C,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$3B,$3C,$3D,$48,$49,$4A,$4B,$47,$48,$49,$4A
.byte $4B,$47,$48,$49,$4A,$4B,$47,$48,$49,$4A,$4B,$47,$3D,$3E,$3F,$00
.byte $09,$0A,$0B,$0C,$00,$00,$00,$00,$00,$00,$00,$05,$06,$07,$08,$00
.byte $00,$00,$15,$00,$00,$00,$09,$0A,$0B,$0C,$00,$00,$00,$00,$00,$00
.byte $00,$00,$01,$02,$03,$04,$00,$00,$00,$00,$00,$00,$00,$01,$02,$03
.byte $04,$00,$00,$00,$00,$00,$00,$00,$00,$05,$06,$07,$08,$00,$00,$00
.byte $00,$00,$00,$00,$09,$0A,$0B,$0C,$00,$00,$00,$00,$00,$3B,$3C,$3D
.byte $48,$49,$4A,$4B,$47,$3D,$48,$49,$4A,$4B,$47,$3D,$48,$49,$4A,$4B
.byte $47,$3D,$3E,$3F,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$11
.byte $12,$13,$14,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$15,$00,$00,$00,$05,$06,$07,$08,$00,$00,$00,$15,$00
.byte $05,$06,$07,$08,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15
.byte $00,$00,$00,$00,$00,$0D,$0E,$0F,$10,$00,$00,$00,$00,$00,$00,$00
.byte $05,$06,$07,$08,$00,$3B,$3C,$3D,$3E,$3F,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$15,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$05,$06,$07,$08,$00,$00,$15,$00,$00,$00,$0D,$0E,$0F,$10
.byte $00,$00,$00,$00,$00,$00,$00,$09,$0A,$0B,$0C,$00,$3B,$3C,$3D,$3E
.byte $3F,$00,$0D,$0E,$0F,$10,$00,$00,$00,$00,$00,$00,$00,$00,$09,$0A
.byte $0B,$0C,$00,$00,$00,$00,$00,$00,$00,$09,$0A,$0B,$0C,$00,$00,$00
.byte $00,$00,$00,$00,$22,$23,$24,$25,$26,$40,$41,$42,$43,$44,$45,$46
.byte $23,$24,$25,$26,$27,$00,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00
.byte $00,$15,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$00
.byte $00,$05,$06,$07,$08,$00,$00,$00,$00,$00,$00,$01,$02,$03,$04,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$15
.byte $00,$00,$00,$09,$0A,$0B,$0C,$00,$00,$00,$15,$00,$09,$0A,$0B,$0C
.byte $00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00
.byte $00,$01,$02,$03,$04,$00,$00,$00,$00,$00,$00,$00,$09,$0A,$0B,$0C
.byte $00,$00,$00,$15,$00,$00,$00,$3B,$3C,$3D,$48,$49,$4A,$4B,$47,$3D
.byte $3E,$3F,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$09,$0A
.byte $0B,$0C,$00,$00,$15,$00,$00,$00,$01,$02,$03,$04,$00,$00,$00,$00
.byte $00,$00,$00,$0D,$0E,$0F,$10,$00,$00,$00,$15,$00,$00,$00,$16,$17
.byte $18,$19,$00,$00,$00,$00,$00,$00,$00,$00,$0D,$0E,$0F,$10,$00,$00
.byte $00,$00,$00,$00,$00,$0D,$0E,$0F,$10,$00,$00,$00,$00,$00,$00,$00
.byte $00,$0D,$0E,$0F,$10,$00,$00,$00,$00,$00,$00,$00,$01,$02,$03,$04
.byte $00,$00,$00,$00,$00,$00,$00,$15,$00,$00,$00,$3B,$3C,$3D,$3E,$3F
.byte $00,$00,$00,$15,$00,$00,$00,$3B,$3C,$3D,$3E,$3F,$00,$09,$0A,$0B
.byte $0C,$00,$00,$00,$00,$00,$00,$09,$0A,$0B,$0C,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$0D
.byte $0E,$0F,$10,$00,$00,$00,$15,$00,$0D,$0E,$0F,$10,$00,$00,$00,$3B
.byte $3C,$3D,$48,$49,$4A,$4B,$47,$3D,$3E,$3F,$00,$00,$22,$23,$24,$25
.byte $26,$40,$41,$42,$43,$44,$45,$46,$23,$24,$25,$26,$27,$00,$00,$15
.byte $00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00
.byte $00,$15,$00,$00,$00,$00,$00,$00,$00,$00,$0D,$0E,$0F,$10,$00,$00
.byte $15,$00,$00,$22,$23,$24,$25,$26,$27,$00,$00,$00,$00,$00,$22,$23
.byte $24,$25,$26,$27,$00,$00,$15,$00,$00,$00,$01,$02,$03,$04,$00,$00
.byte $00,$00,$00,$00,$00,$00,$16,$17,$18,$19,$00,$00,$00,$00,$00,$00
.byte $00,$01,$02,$03,$04,$00,$00,$00,$00,$00,$00,$00,$00,$16,$17,$18
.byte $19,$00,$00,$00,$00,$00,$00,$00,$11,$12,$13,$14,$00,$00,$00,$00
.byte $00,$00,$00,$15,$00,$00,$00,$05,$06,$07,$08,$00,$00,$00,$00,$15
.byte $00,$00,$00,$00,$00,$15,$00,$00,$00,$0D,$0E,$0F,$10,$00,$00,$00
.byte $00,$00,$00,$0D,$0E,$0F,$10,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$16,$17,$18,$19,$00
.byte $00,$00,$15,$00,$16,$17,$18,$19,$00,$00,$00,$00,$00,$15,$00,$00
.byte $00,$00,$00,$15,$00,$00,$00,$00,$00,$16,$17,$18,$19,$00,$00,$00
.byte $00,$00,$00,$00,$16,$17,$18,$19,$00,$00,$00,$15,$00,$00,$00,$00
.byte $00,$15,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$00
.byte $00,$00,$00,$00,$00,$00,$16,$17,$18,$19,$00,$00,$15,$00,$00,$00
.byte $16,$17,$18,$19,$00,$00,$00,$00,$00,$00,$00,$16,$17,$18,$19,$00
.byte $00,$00,$15,$00,$00,$22,$23,$24,$25,$26,$40,$41,$42,$43,$44,$45
.byte $46,$23,$24,$25,$26,$27,$00,$00,$00,$00,$00,$00,$00,$11,$12,$13
.byte $14,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02,$03,$04,$00,$00,$00
.byte $00,$00,$00,$22,$23,$24,$25,$26,$27,$00,$00,$00,$00,$3B,$3C,$3D
.byte $3E,$3F,$00,$09,$0A,$0B,$0C,$00,$00,$3B,$3C,$3D,$3E,$3F,$00,$00
.byte $00,$15,$00,$00,$00,$16,$17,$18,$19,$00,$00,$00,$00,$00,$00,$16
.byte $17,$18,$19,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$28,$29,$2A,$2B,$2C,$2D,$00,$00,$15,$00
.byte $01,$02,$03,$04,$00,$00,$00,$00,$00,$15,$00,$00,$00,$3B,$3C,$3D
.byte $3E,$3F,$00,$00,$28,$29,$2A,$2B,$2C,$2D,$00,$00,$00,$00,$00,$00
.byte $01,$02,$03,$04,$00,$3B,$3C,$3D,$48,$49,$4A,$4B,$47,$3D,$48,$49
.byte $4A,$4B,$47,$3D,$48,$49,$4A,$4B,$47,$3D,$3E,$3F,$00,$00,$00,$00
.byte $00,$00,$01,$02,$03,$04,$00,$00,$15,$00,$00,$28,$29,$2A,$2B,$2C
.byte $2D,$00,$00,$00,$00,$00,$28,$29,$2A,$2B,$2C,$2D,$00,$00,$15,$00
.byte $00,$00,$01,$02,$03,$04,$00,$00,$00,$00,$00,$00,$28,$29,$2A,$2B
.byte $2C,$2D,$00,$00,$00,$00,$00,$00,$22,$23,$24,$25,$26,$40,$41,$42
.byte $43,$44,$45,$46,$23,$24,$25,$26,$27,$00,$00,$00,$00,$00,$00,$28
.byte $29,$2A,$2B,$2C,$2D,$00,$00,$00,$00,$00,$00,$15,$00,$00,$00,$01
.byte $02,$03,$04,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$00
.byte $28,$29,$2A,$2B,$2C,$2D,$00,$00,$00,$00,$22,$23,$24,$25,$26,$27
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$1A,$1B,$1C,$1D,$1E,$1F,$20,$21,$00,$15,$00,$11,$12,$13,$14
.byte $00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$00,$00,$1A
.byte $1B,$1C,$1D,$1E,$1F,$20,$21,$00,$00,$00,$00,$00,$11,$12,$13,$14
.byte $00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$15
.byte $00,$00,$00,$00,$00,$15,$00,$00,$00,$00,$00,$00,$00,$00,$11,$12
.byte $13,$14,$00,$00,$15,$00,$1A,$1B,$1C,$1D,$1E,$1F,$20,$21,$00,$00
.byte $00,$1A,$1B,$1C,$1D,$1E,$1F,$20,$21,$00,$15,$00,$00,$00,$11,$12
.byte $13,$14,$00,$00,$00,$00,$00,$1A,$1B,$1C,$1D,$1E,$1F,$20,$21,$00
.byte $00,$00,$00,$1A,$1B,$1C,$1D,$1E,$1F,$20,$21,$00,$00,$00,$1A,$1B
.byte $1C,$1D,$1E,$1F,$20,$21,$00,$00,$00,$00,$1A,$1B,$1C,$1D,$1E,$1F
.byte $20,$21,$00,$00,$00,$00,$00,$15,$00,$00,$00,$11,$12,$13,$14,$00
.byte $00,$00,$00,$15,$00,$00,$00,$00,$00,$15,$00,$1A,$1B,$1C,$1D,$1E
.byte $1F,$20,$21,$00,$00,$1A,$1B,$1C,$1D,$1E,$1F,$20,$21,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$2E,$2E,$2E,$2E,$2E,$2E,$2F,$30
.byte $31,$32,$33,$34,$2E,$2E,$35,$36,$37,$38,$39,$3A,$2E,$2E,$2E,$2E
.byte $2E,$35,$36,$2E,$2E,$2E,$2E,$35,$36,$2E,$2E,$2E,$2F,$30,$31,$32
.byte $33,$34,$2E,$2E,$2E,$2E,$2E,$2E,$37,$38,$39,$3A,$2E,$2E,$2E,$35
.byte $36,$2E,$2E,$2E,$2E,$35,$36,$2E,$2E,$2E,$2E,$35,$36,$2E,$2E,$2E
.byte $2E,$35,$36,$2E,$2E,$2E,$2E,$2E,$2E,$2E,$37,$38,$39,$3A,$2E,$2E
.byte $35,$36,$2E,$2F,$30,$31,$32,$33,$34,$2E,$2E,$2E,$2E,$2E,$2F,$30
.byte $31,$32,$33,$34,$2E,$2E,$35,$36,$2E,$2E,$37,$38,$39,$3A,$2E,$2E
.byte $2E,$2E,$2E,$2E,$2F,$30,$31,$32,$33,$34,$2E,$2E,$2E,$2E,$2E,$2E
.byte $2F,$30,$31,$32,$33,$34,$2E,$2E,$2E,$2E,$2E,$2F,$30,$31,$32,$33
.byte $34,$2E,$2E,$2E,$2E,$2E,$2E,$2F,$30,$31,$32,$33,$34,$2E,$2E,$2E
.byte $2E,$2E,$2E,$35,$36,$2E,$2E,$37,$38,$39,$3A,$2E,$2E,$2E,$2E,$35
.byte $36,$2E,$2E,$2E,$2E,$35,$36,$2E,$2F,$30,$31,$32,$33,$34,$2E,$2E
.byte $2E,$2E,$2F,$30,$31,$32,$33,$34,$2E,$2E,$2E,$2E,$2E,$2E,$2E,$2E
.byte $2E,$2E,$2E,$2E,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
.byte $4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C,$4C
