- todo
  - layered parallax scrolling test
    - prepare charsets
    - prepare level maps
    - write d018 switcher
    - write scroll routine
  - stable raster irq test
  - vsp test
  - combined test

- vsp scrolling
- parallax scrolling with 3 map layers + 2 in-char layers
- pixel level stacked parallax scrolling
- foreground layer with sprites

- vertical scroll animation on parallax blocks
- in-place char animations

- split d021-d022-d023 colors for map coloring
- ecm + d021-d022-d023 split colors
- interlaced/chequered colors
- unusual main background color (white etc.)
- raster based gradients

- open upper/lower borders for hud

- screen vibration effects
- transparency effects

- double-speed music and sound effects
- digi sound effects

- sprite multiplexer
- char mode projectiles

- undocumented opcodes
