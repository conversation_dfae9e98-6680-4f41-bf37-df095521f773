
; Generated by CharPad 2.8, Subchrist Software, 2021.
; Assemble with 64TASS or similar.


; Character display mode : Multi-colour.

; Character colouring method : Per-Character.


; Colour values...

COLR_SCREEN = 14
COLR_CHAR_DEF = 8
COLR_CHAR_MC1 = 9
COLR_CHAR_MC2 = 10


; Quantities and dimensions...

CHAR_COUNT = 141
MAP_WID = 400
MAP_HEI = 18


; Data block sizes (in bytes)...

SZ_CHARSET_DATA        = 1128 ; ($468)
SZ_CHARSET_ATTRIB_DATA = 141 ; ($8D)
SZ_MAP_DATA            = 7200 ; ($1C20)




; * INSERT EXAMPLE PROGRAM HERE! * (Or just include this file in your project).




; CHARSET IMAGE DATA...
; 141 images, 8 bytes per image, total size is 1128 ($468) bytes.

charset_data

.byte $00,$00,$00,$00,$00,$00,$00,$00,$7D,$75,$F6,$D9,$F5,$F6,$DD,$76
.byte $6E,$B9,$6E,$B9,$A9,$6E,$B9,$6E,$D5,$F6,$DD,$D6,$F6,$FD,$75,$7D
.byte $B9,$6E,$BA,$B9,$69,$6E,$B9,$6E,$7D,$F6,$FD,$7D,$76,$FD,$D6,$F6
.byte $B9,$F9,$AE,$B9,$6E,$BE,$B9,$EE,$55,$5A,$AB,$BF,$EE,$FB,$AA,$E6
.byte $55,$A5,$B9,$FE,$EF,$BB,$EA,$AE,$55,$95,$6A,$BE,$FB,$AE,$EB,$BA
.byte $55,$59,$6A,$AE,$BE,$FB,$AE,$BA,$55,$66,$59,$96,$95,$E5,$A5,$B5
.byte $99,$FF,$00,$00,$FF,$FF,$55,$55,$00,$00,$00,$00,$00,$00,$00,$6E
.byte $F3,$F3,$F3,$F3,$F3,$F3,$F3,$F3,$5F,$75,$9D,$9D,$75,$76,$5D,$FE
.byte $55,$95,$17,$1B,$26,$06,$09,$01,$00,$00,$80,$80,$E0,$E0,$B8,$B8
.byte $00,$02,$01,$09,$06,$26,$1B,$9B,$55,$59,$6D,$B8,$B8,$E0,$E0,$80
.byte $DD,$7E,$BD,$9D,$6D,$5F,$F5,$FF,$D9,$76,$75,$7E,$BD,$BD,$BF,$BD
.byte $BD,$7F,$7D,$FF,$7D,$7F,$F7,$D7,$00,$00,$01,$05,$0D,$35,$3D,$37
.byte $17,$75,$55,$5D,$DD,$5D,$7F,$7F,$54,$99,$E7,$E6,$F9,$F9,$79,$7D
.byte $00,$00,$40,$90,$D0,$A4,$74,$74,$00,$00,$40,$90,$D0,$E4,$F4,$B4
.byte $00,$00,$03,$05,$0D,$15,$35,$35,$15,$5D,$76,$76,$75,$76,$F5,$D6
.byte $54,$7D,$DE,$F7,$B6,$FD,$BD,$BD,$06,$01,$00,$00,$00,$03,$0F,$3F
.byte $00,$80,$80,$40,$80,$7C,$AF,$EB,$55,$55,$5A,$56,$5B,$1B,$16,$1B
.byte $EE,$B0,$E0,$C0,$80,$00,$00,$00,$7F,$F5,$DD,$F7,$7F,$7D,$F5,$DD
.byte $77,$99,$56,$55,$77,$D5,$99,$56,$F7,$7F,$7F,$D6,$F5,$DD,$F7,$7F
.byte $55,$77,$55,$6A,$99,$56,$55,$77,$FD,$F6,$DA,$56,$D9,$D5,$77,$FF
.byte $90,$64,$E9,$DA,$F6,$FD,$76,$7D,$AA,$55,$55,$AA,$55,$57,$5F,$FD
.byte $AB,$5D,$76,$F5,$D9,$67,$9D,$5D,$6A,$D5,$F5,$BD,$6B,$5A,$A6,$55
.byte $AA,$55,$55,$AA,$95,$E5,$B9,$7D,$56,$55,$DD,$77,$FF,$FF,$FF,$FF
.byte $59,$D7,$7D,$DF,$FF,$FF,$FF,$FF,$69,$96,$75,$56,$DD,$FF,$F5,$FD
.byte $AA,$59,$65,$57,$DD,$F7,$FD,$5F,$A6,$9D,$55,$77,$DD,$FF,$7F,$D7
.byte $A9,$66,$D5,$5D,$F7,$DF,$FF,$F5,$AF,$6F,$FF,$7F,$FF,$FD,$FF,$F5
.byte $59,$66,$59,$97,$5E,$75,$D5,$FF,$6B,$BF,$D7,$FF,$AA,$55,$55,$FF
.byte $E9,$FE,$7F,$FF,$A5,$57,$55,$FF,$56,$59,$65,$D6,$5A,$F5,$FE,$FD
.byte $9F,$9F,$9F,$9F,$9F,$9F,$9F,$9F,$59,$55,$B5,$37,$1D,$0F,$07,$01
.byte $6E,$6E,$B8,$B8,$E0,$E0,$80,$80,$00,$00,$00,$00,$00,$05,$16,$B6
.byte $00,$00,$00,$00,$00,$40,$90,$E0,$FD,$F5,$FD,$FD,$F5,$FD,$F5,$DD
.byte $1A,$1B,$1A,$1B,$26,$06,$07,$09,$F7,$D7,$F4,$D4,$34,$35,$0D,$00
.byte $5F,$57,$1F,$17,$15,$07,$05,$01,$79,$79,$78,$64,$60,$50,$40,$00
.byte $79,$6D,$5D,$5D,$59,$54,$14,$10,$74,$79,$6D,$59,$5D,$59,$58,$04
.byte $7D,$75,$FD,$F5,$74,$3C,$1C,$00,$D5,$F5,$D5,$F4,$74,$74,$15,$05
.byte $6F,$5B,$6F,$5B,$57,$15,$06,$01,$1D,$17,$1D,$17,$15,$05,$05,$01
.byte $BB,$EF,$FB,$7F,$FB,$7C,$54,$50,$5F,$07,$01,$01,$00,$00,$00,$00
.byte $7F,$D6,$D5,$D6,$F5,$DD,$F7,$7F,$FF,$A9,$AA,$6A,$99,$56,$55,$57
.byte $FF,$FF,$DF,$FD,$7F,$FF,$DD,$77,$FF,$FF,$FF,$75,$FF,$FD,$56,$55
.byte $7D,$96,$6A,$A9,$96,$75,$DF,$FF,$FF,$5F,$97,$A7,$67,$DF,$77,$DF
.byte $9D,$76,$DA,$56,$D9,$D5,$77,$9F,$74,$DD,$67,$A9,$9A,$75,$DD,$57
.byte $5F,$D5,$76,$9A,$A9,$A5,$5F,$F5,$F4,$59,$A5,$69,$5A,$D5,$5F,$75
.byte $5F,$75,$9A,$9A,$A9,$65,$D7,$7D,$FF,$FD,$F7,$FD,$F6,$FD,$D6,$F6
.byte $55,$55,$55,$B5,$69,$BE,$B9,$EE,$D6,$59,$69,$65,$A7,$5D,$FD,$76
.byte $AA,$59,$67,$5F,$DF,$FF,$FF,$7F,$A6,$9D,$FF,$FF,$FF,$FF,$FF,$FF
.byte $A9,$66,$D5,$F5,$FD,$FD,$FD,$FF,$00,$00,$40,$D0,$BD,$5A,$75,$AE
.byte $AA,$55,$55,$FF,$AA,$55,$55,$FF,$AA,$55,$56,$F9,$99,$59,$5F,$FF
.byte $AA,$55,$A9,$57,$FE,$95,$95,$FF,$57,$FF,$FF,$FF,$57,$FF,$FF,$FF
.byte $FD,$E4,$50,$80,$80,$40,$00,$00,$FF,$FF,$FD,$F5,$FD,$F5,$D9,$D6
.byte $7F,$1F,$07,$01,$02,$02,$00,$00,$59,$76,$75,$7D,$75,$BD,$1F,$27
.byte $A8,$64,$94,$54,$54,$70,$F0,$C0,$FF,$FF,$FF,$FF,$FF,$FF,$FF,$FF
.byte $56,$5D,$7F,$7F,$FF,$7F,$FF,$DD,$69,$76,$D6,$FD,$F5,$FD,$FD,$D5
.byte $D5,$FD,$FF,$FD,$FD,$FF,$FD,$FD,$F5,$D4,$D2,$D9,$D7,$D7,$D9,$F7
.byte $55,$18,$65,$49,$DB,$77,$F7,$7D,$55,$82,$61,$99,$57,$DD,$FF,$F7
.byte $75,$16,$89,$65,$DB,$F7,$FF,$DF,$5D,$A6,$41,$99,$75,$D7,$FD,$7F
.byte $55,$48,$06,$99,$77,$F5,$FF,$DF,$7D,$96,$89,$66,$DD,$FF,$77,$DF
.byte $57,$A1,$58,$64,$5D,$55,$D7,$59,$57,$61,$92,$89,$52,$69,$D9,$D2
.byte $D9,$F6,$D5,$F7,$F5,$F6,$F5,$FD,$F7,$FF,$F7,$FF,$FF,$7F,$FD,$F5
.byte $F7,$FF,$F7,$7F,$FF,$77,$9F,$27,$FF,$FF,$FF,$DF,$7F,$DF,$67,$57
.byte $F6,$F6,$DD,$F6,$F4,$DD,$76,$D5,$FD,$7F,$FD,$FD,$77,$F7,$7F,$5F
.byte $F6,$DA,$D9,$76,$D9,$FA,$76,$D9,$D6,$59,$F6,$75,$D9,$DA,$59,$66
.byte $55,$AA,$55,$FF,$FD,$F6,$FD,$75,$55,$AA,$55,$FF,$FF,$FF,$FF,$DD
.byte $D9,$5A,$76,$77,$FD,$77,$FD,$7D,$77,$7F,$FF,$FF,$7F,$FF,$FF,$FD
.byte $56,$75,$DE,$F5,$FD,$F7,$FD,$DD,$56,$75,$7F,$FF,$7F,$7F,$FF,$FD
.byte $99,$56,$D5,$75,$D6,$FD,$FD,$75,$7D,$DF,$67,$EF,$47,$6F,$D7,$57
.byte $D9,$61,$D9,$F7,$DB,$F7,$7D,$F5,$61,$D9,$63,$6B,$D7,$6F,$9F,$6F
.byte $FF,$FF,$FF,$FF,$F6,$7D,$F6,$DD,$FF,$FF,$FF,$FF,$F7,$7D,$FF,$F7
.byte $FF,$FF,$FF,$F6,$F6,$DA,$F6,$7D,$FF,$FF,$FE,$F5,$FD,$DB,$D6,$F6
.byte $DA,$AD,$67,$5B,$5F,$BF,$FF,$FF,$AF,$5A,$5B,$6D,$F5,$FD,$FF,$FF
.byte $FF,$FF,$BF,$6F,$6F,$9B,$57,$5F,$6B,$DB,$D7,$FF,$A7,$5B,$DB,$F7
.byte $DA,$F6,$FE,$FF,$E9,$D6,$F6,$FD


; CHARSET IMAGE ATTRIBUTE DATA...
; 141 attributes, 1 attribute per image, 8 bits per attribute, total size is 141 ($8D) bytes.
; nb. Upper nybbles = material, lower nybbles = colour.

charset_attrib_data

.byte $80,$88,$8F,$88,$8F,$88,$8F,$89,$89,$89,$89,$89,$0E,$89,$8E,$88
.byte $8F,$8F,$8F,$8F,$88,$88,$88,$88,$8D,$8D,$8F,$8F,$88,$8D,$8D,$8A
.byte $8A,$8F,$8F,$88,$88,$88,$88,$08,$88,$88,$88,$8F,$8F,$88,$88,$88
.byte $88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$8F,$88,$89,$88,$8F,$88
.byte $8D,$8D,$8F,$8F,$88,$8D,$8D,$8A,$8A,$88,$88,$88,$88,$88,$08,$08
.byte $08,$08,$08,$08,$08,$88,$8F,$08,$88,$88,$88,$8F,$88,$88,$88,$88
.byte $88,$88,$88,$88,$88,$88,$88,$88,$88,$08,$08,$08,$08,$08,$08,$08
.byte $08,$08,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88
.byte $88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88,$88


; MAP DATA...
; 400x18 (7200) cells, 8 bits per cell, total size is 7200 ($1C20) bytes.

map_data

.byte $07,$08,$09,$0A,$07,$08,$09,$0A,$07,$07,$08,$09,$0A,$07,$08,$09
.byte $0A,$07,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A
.byte $07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A
.byte $07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A
.byte $07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$09,$08,$07,$09
.byte $08,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$09,$08,$07
.byte $09,$08,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$09,$08
.byte $07,$09,$08,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$09
.byte $08,$07,$09,$08,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A
.byte $09,$08,$07,$09,$08,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09
.byte $0A,$09,$08,$07,$09,$08,$0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09
.byte $0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09,$0A,$0A,$07,$08,$09,$0A
.byte $07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A,$07,$08,$09,$0A
.byte $07,$08,$09,$0A,$07,$08,$09,$0A,$07,$09,$08,$07,$09,$08,$0A,$07
.byte $08,$09,$0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09,$0A,$09,$08,$07
.byte $09,$08,$0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09
.byte $0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08
.byte $0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09,$0A,$09
.byte $08,$07,$09,$08,$0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08,$0A,$07
.byte $08,$09,$0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09,$0A,$09,$08,$07
.byte $09,$08,$0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09
.byte $0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08
.byte $0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08,$0A,$07,$08,$09,$0A,$09
.byte $08,$0A,$07,$08,$09,$0A,$09,$08,$0A,$09,$08,$07,$09,$08,$0A,$07
.byte $08,$09,$0A,$0A,$07,$0A,$0A,$07,$08,$09,$0A,$09,$08,$07,$09,$08
.byte $2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$2F,$30,$31,$32,$2F,$30,$31
.byte $32,$2F,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32
.byte $2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32
.byte $2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32
.byte $2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$31,$30,$2F,$31
.byte $30,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$31,$30,$2F
.byte $31,$30,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$31,$30
.byte $2F,$31,$30,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$31
.byte $30,$2F,$31,$30,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32
.byte $31,$30,$2F,$31,$30,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31
.byte $32,$31,$30,$2F,$31,$30,$32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31
.byte $32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31,$32,$32,$2F,$30,$31,$32
.byte $2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$30,$31,$32
.byte $2F,$30,$31,$32,$2F,$30,$31,$32,$2F,$31,$30,$2F,$31,$30,$32,$2F
.byte $30,$31,$32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31,$32,$31,$30,$2F
.byte $31,$30,$32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31
.byte $32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30
.byte $32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31,$32,$31
.byte $30,$2F,$31,$30,$32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30,$32,$2F
.byte $30,$31,$32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31,$32,$31,$30,$2F
.byte $31,$30,$32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31
.byte $32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30
.byte $32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30,$32,$2F,$30,$31,$32,$31
.byte $30,$32,$2F,$30,$31,$32,$31,$30,$32,$31,$30,$2F,$31,$30,$32,$2F
.byte $30,$31,$32,$32,$2F,$32,$32,$2F,$30,$31,$32,$31,$30,$2F,$31,$30
.byte $3B,$3C,$00,$00,$00,$1F,$20,$00,$00,$00,$00,$00,$10,$11,$55,$56
.byte $12,$13,$49,$21,$22,$00,$00,$49,$21,$22,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$1F,$20,$10,$11,$65,$3D,$00,$00,$55,$56,$12,$13,$00
.byte $00,$49,$21,$22,$00,$00,$3B,$3C,$00,$09,$0B,$55,$56,$07,$08,$09
.byte $0A,$07,$08,$09,$0B,$55,$56,$0A,$07,$08,$00,$1F,$20,$00,$00,$00
.byte $10,$11,$4A,$4B,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $65,$4A,$4B,$12,$13,$00,$10,$11,$55,$56,$12,$13,$00,$49,$21,$22
.byte $00,$10,$11,$55,$56,$12,$13,$00,$49,$21,$22,$00,$10,$11,$55,$56
.byte $12,$13,$49,$21,$22,$00,$00,$1F,$20,$00,$00,$55,$56,$12,$13,$49
.byte $21,$22,$10,$11,$55,$56,$12,$13,$00,$00,$00,$17,$18,$19,$1A,$00
.byte $10,$11,$65,$3D,$00,$55,$56,$12,$13,$00,$10,$11,$65,$3D,$00,$55
.byte $56,$12,$13,$00,$00,$49,$21,$22,$10,$11,$55,$56,$12,$13,$00,$00
.byte $49,$21,$22,$00,$00,$00,$00,$00,$00,$00,$00,$00,$49,$21,$22,$00
.byte $10,$11,$55,$56,$12,$13,$00,$1F,$20,$00,$00,$00,$00,$00,$49,$21
.byte $22,$00,$17,$18,$19,$1A,$00,$00,$00,$1F,$20,$00,$00,$00,$49,$21
.byte $22,$00,$00,$10,$11,$55,$56,$00,$65,$3D,$12,$13,$00,$10,$11,$65
.byte $3D,$00,$55,$56,$00,$00,$00,$00,$1C,$1D,$1E,$1B,$10,$11,$65,$3D
.byte $00,$55,$56,$12,$13,$49,$21,$22,$10,$11,$55,$56,$00,$00,$55,$56
.byte $12,$13,$00,$00,$49,$21,$22,$00,$00,$00,$00,$10,$11,$65,$3D,$12
.byte $13,$4A,$4B,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $65,$65,$65,$65,$55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $63,$64,$00,$00,$00,$47,$48,$00,$00,$00,$00,$00,$00,$39,$01,$02
.byte $3A,$00,$00,$3E,$00,$00,$00,$1C,$1D,$1E,$1B,$00,$00,$00,$00,$00
.byte $00,$00,$00,$47,$48,$00,$39,$55,$56,$00,$00,$05,$06,$3A,$00,$00
.byte $00,$00,$3E,$00,$00,$00,$63,$64,$00,$31,$33,$01,$02,$2F,$30,$31
.byte $32,$2F,$30,$31,$33,$01,$02,$32,$2F,$30,$00,$47,$48,$00,$00,$00
.byte $00,$39,$4A,$4B,$65,$65,$65,$65,$65,$65,$7D,$68,$66,$67,$65,$65
.byte $01,$02,$66,$68,$7D,$7E,$7F,$80,$65,$65,$65,$65,$65,$65,$65,$65
.byte $01,$02,$65,$68,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $01,$02,$65,$65,$65,$65,$65,$65,$65,$65,$7D,$68,$65,$65,$65,$65
.byte $01,$02,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $65,$4C,$4D,$3A,$00,$00,$00,$39,$03,$04,$3A,$00,$00,$00,$3E,$00
.byte $00,$00,$39,$03,$04,$3A,$00,$00,$00,$3E,$00,$00,$00,$39,$01,$02
.byte $3A,$00,$17,$18,$19,$1A,$00,$47,$48,$00,$00,$05,$06,$3A,$00,$00
.byte $3E,$00,$00,$39,$01,$02,$3A,$00,$00,$00,$00,$3F,$40,$41,$42,$00
.byte $00,$39,$55,$56,$00,$01,$02,$3A,$3B,$3C,$00,$39,$55,$56,$00,$01
.byte $02,$3A,$00,$00,$00,$00,$3E,$00,$00,$39,$01,$02,$3A,$00,$00,$00
.byte $17,$18,$19,$1A,$00,$00,$00,$00,$00,$00,$00,$00,$00,$3E,$00,$00
.byte $00,$39,$01,$02,$3A,$00,$00,$47,$48,$00,$28,$5B,$00,$00,$00,$3E
.byte $00,$00,$3F,$40,$41,$42,$00,$00,$00,$47,$48,$00,$00,$00,$00,$3E
.byte $00,$00,$00,$00,$39,$01,$02,$00,$55,$56,$3A,$3B,$3C,$00,$39,$55
.byte $56,$00,$01,$02,$00,$28,$5B,$00,$44,$45,$46,$43,$00,$39,$55,$56
.byte $00,$01,$02,$3A,$00,$00,$3E,$00,$00,$39,$01,$02,$00,$00,$01,$02
.byte $3A,$00,$00,$00,$17,$18,$19,$1A,$00,$00,$00,$00,$39,$55,$56,$3A
.byte $00,$4C,$4D,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$7D,$68
.byte $66,$67,$65,$65,$01,$02,$65,$65,$65,$65,$65,$65,$65,$65,$7D,$68
.byte $07,$09,$00,$0D,$00,$00,$0D,$00,$00,$00,$00,$00,$00,$00,$05,$06
.byte $00,$00,$17,$18,$19,$1A,$00,$44,$45,$46,$43,$00,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$03,$04,$00,$00,$00
.byte $00,$17,$18,$19,$1A,$00,$08,$09,$0A,$07,$0B,$05,$06,$0A,$07,$08
.byte $09,$0A,$07,$08,$0B,$05,$06,$08,$09,$00,$00,$0D,$00,$00,$0D,$00
.byte $00,$00,$4A,$4B,$65,$65,$65,$65,$65,$65,$65,$7D,$7E,$66,$68,$65
.byte $05,$06,$65,$66,$67,$66,$68,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $05,$06,$68,$7F,$80,$65,$65,$65,$65,$65,$65,$65,$65,$7D,$7E,$65
.byte $05,$06,$65,$65,$65,$7F,$68,$65,$65,$65,$65,$7D,$7E,$65,$65,$65
.byte $05,$06,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$7D,$7E,$65
.byte $65,$23,$24,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$1F,$20
.byte $00,$00,$00,$01,$02,$00,$00,$00,$1C,$1D,$1E,$1B,$00,$00,$05,$06
.byte $00,$00,$3F,$40,$41,$42,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00
.byte $1F,$20,$00,$00,$05,$06,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$03,$04,$00,$05,$06,$00,$63,$64,$00,$00,$01,$02,$00,$05
.byte $06,$00,$00,$00,$00,$00,$1F,$20,$00,$00,$05,$04,$00,$00,$00,$00
.byte $3F,$40,$41,$42,$00,$00,$28,$5B,$00,$00,$00,$00,$1C,$1D,$1E,$1B
.byte $00,$00,$05,$04,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$1F
.byte $20,$00,$00,$00,$00,$0D,$00,$00,$0D,$00,$3B,$3C,$00,$00,$1C,$1D
.byte $1E,$1B,$00,$00,$00,$03,$04,$00,$05,$06,$00,$63,$64,$00,$00,$01
.byte $02,$00,$05,$06,$00,$05,$06,$00,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$05,$06,$00,$00,$00,$1F,$20,$00,$00,$05,$06,$00,$00,$05,$06
.byte $00,$00,$00,$00,$3F,$40,$41,$42,$00,$00,$3B,$3C,$00,$03,$04,$00
.byte $00,$23,$24,$66,$67,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$7D
.byte $7E,$66,$68,$65,$05,$06,$65,$65,$65,$7F,$68,$65,$65,$65,$65,$7D
.byte $30,$31,$00,$0E,$00,$00,$0E,$00,$00,$00,$00,$00,$00,$00,$03,$04
.byte $00,$00,$3F,$40,$41,$42,$00,$00,$00,$00,$00,$00,$00,$00,$28,$5B
.byte $00,$00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$01,$02,$00,$00,$00
.byte $00,$3F,$40,$41,$42,$00,$30,$31,$32,$2F,$33,$03,$04,$32,$2F,$30
.byte $31,$32,$2F,$30,$33,$03,$04,$30,$31,$00,$00,$0E,$00,$00,$0E,$00
.byte $00,$00,$4A,$4B,$65,$68,$65,$61,$60,$00,$62,$68,$66,$67,$66,$65
.byte $03,$04,$66,$67,$66,$68,$65,$61,$60,$00,$62,$68,$7F,$80,$65,$65
.byte $03,$04,$7D,$7E,$7F,$80,$65,$61,$60,$00,$62,$68,$7F,$80,$7D,$65
.byte $03,$04,$65,$65,$7F,$80,$65,$61,$60,$00,$62,$68,$66,$67,$65,$65
.byte $03,$04,$65,$65,$65,$65,$65,$61,$60,$00,$62,$68,$7F,$80,$7D,$68
.byte $65,$25,$26,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$47,$48
.byte $00,$00,$00,$03,$04,$00,$00,$00,$44,$45,$46,$43,$00,$00,$03,$04
.byte $00,$00,$00,$00,$00,$00,$00,$28,$5B,$00,$00,$01,$02,$00,$00,$00
.byte $47,$48,$00,$00,$03,$04,$00,$00,$00,$00,$00,$28,$5B,$00,$00,$00
.byte $50,$52,$51,$52,$52,$51,$52,$53,$51,$52,$52,$51,$52,$53,$51,$52
.byte $53,$51,$57,$00,$00,$00,$47,$48,$00,$00,$01,$02,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$44,$45,$46,$43
.byte $00,$00,$01,$02,$00,$00,$00,$00,$00,$00,$05,$04,$00,$00,$00,$47
.byte $48,$00,$00,$00,$00,$0E,$00,$00,$0E,$00,$63,$64,$00,$00,$44,$45
.byte $46,$43,$00,$50,$52,$51,$52,$52,$51,$52,$53,$51,$52,$52,$51,$52
.byte $53,$51,$52,$53,$51,$51,$53,$51,$52,$52,$51,$52,$53,$51,$52,$53
.byte $51,$52,$53,$52,$57,$00,$47,$48,$00,$00,$01,$02,$00,$00,$03,$04
.byte $00,$00,$00,$00,$00,$00,$00,$00,$28,$5B,$63,$64,$00,$03,$04,$00
.byte $00,$25,$26,$67,$7F,$80,$65,$65,$65,$65,$65,$61,$60,$00,$62,$68
.byte $66,$67,$66,$65,$03,$04,$65,$65,$7F,$80,$65,$61,$60,$00,$62,$68
.byte $07,$0B,$07,$0E,$07,$00,$0E,$00,$00,$00,$00,$00,$00,$00,$05,$06
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$00,$00,$05,$06,$00,$00,$03,$04,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$00,$00,$09,$09,$0B,$01,$02,$09,$0A,$07
.byte $08,$09,$0A,$07,$0B,$01,$02,$09,$0A,$07,$0B,$0E,$07,$00,$0E,$00
.byte $00,$00,$4A,$4B,$7C,$7D,$7E,$79,$00,$00,$00,$66,$67,$65,$65,$65
.byte $01,$02,$80,$7D,$7E,$65,$65,$79,$00,$00,$00,$66,$67,$66,$68,$65
.byte $01,$02,$7E,$7D,$7E,$66,$67,$79,$00,$00,$00,$66,$67,$66,$68,$65
.byte $01,$02,$65,$66,$67,$7D,$7E,$79,$00,$00,$00,$66,$67,$7D,$7E,$65
.byte $01,$02,$65,$65,$65,$7D,$7E,$79,$00,$00,$00,$66,$67,$66,$68,$66
.byte $65,$4A,$4B,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$00,$00,$05,$06,$00,$00,$03,$04,$00,$00,$00
.byte $00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00
.byte $00,$00,$65,$3D,$00,$55,$56,$00,$00,$00,$00,$00,$65,$3D,$00,$55
.byte $56,$00,$00,$00,$00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00
.byte $00,$00,$00,$00,$50,$52,$53,$54,$51,$57,$00,$00,$00,$00,$00,$00
.byte $00,$00,$03,$04,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00
.byte $00,$00,$08,$09,$0A,$0E,$0C,$0C,$0E,$08,$0A,$07,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$55,$56,$00,$65,$3D,$00,$00,$00,$00,$00,$65
.byte $3D,$00,$55,$56,$00,$65,$3D,$00,$00,$00,$00,$00,$00,$00,$65,$3D
.byte $00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$05,$04
.byte $00,$00,$00,$00,$00,$00,$00,$50,$51,$52,$53,$54,$53,$53,$54,$57
.byte $00,$4A,$4B,$66,$68,$66,$68,$7D,$65,$65,$65,$79,$00,$00,$00,$66
.byte $67,$65,$65,$65,$01,$02,$65,$66,$67,$7D,$7E,$79,$00,$00,$00,$66
.byte $2F,$33,$2F,$0E,$2F,$00,$0E,$00,$00,$00,$00,$50,$52,$52,$53,$51
.byte $52,$52,$53,$51,$52,$53,$54,$51,$52,$53,$54,$51,$51,$52,$53,$51
.byte $52,$53,$54,$51,$52,$53,$54,$51,$52,$53,$54,$51,$52,$53,$54,$52
.byte $53,$51,$52,$53,$54,$51,$57,$00,$2F,$2F,$33,$05,$06,$31,$58,$59
.byte $59,$59,$59,$5A,$33,$01,$02,$31,$32,$2F,$33,$0E,$2F,$00,$0E,$00
.byte $00,$00,$4A,$4B,$66,$67,$7C,$78,$00,$00,$00,$68,$65,$65,$65,$65
.byte $05,$06,$65,$68,$7F,$80,$7C,$78,$00,$00,$00,$7C,$66,$67,$66,$65
.byte $03,$06,$66,$67,$7D,$7E,$7F,$78,$00,$00,$00,$7C,$66,$67,$65,$65
.byte $05,$06,$7F,$80,$66,$67,$7C,$78,$00,$00,$00,$65,$7F,$80,$7F,$65
.byte $05,$06,$65,$65,$7F,$80,$7C,$78,$00,$00,$00,$7C,$66,$67,$66,$67
.byte $65,$4A,$4B,$00,$00,$00,$00,$00,$05,$06,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$05,$06,$00,$00,$00,$00,$00,$00,$00,$50,$52,$53,$51
.byte $52,$53,$54,$51,$52,$53,$54,$51,$52,$53,$54,$51,$52,$53,$54,$52
.byte $53,$51,$52,$53,$51,$52,$53,$54,$51,$53,$54,$51,$52,$57,$00,$00
.byte $00,$00,$55,$56,$00,$01,$02,$00,$00,$00,$00,$00,$55,$56,$00,$01
.byte $02,$00,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$01,$02,$00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00
.byte $00,$00,$30,$31,$32,$0E,$30,$2F,$0E,$30,$32,$2F,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$01,$02,$00,$55,$56,$00,$00,$00,$00,$00,$55
.byte $56,$00,$01,$02,$00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$55,$56
.byte $00,$01,$02,$00,$00,$00,$00,$50,$53,$51,$52,$53,$54,$53,$53,$54
.byte $53,$54,$57,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00,$65,$3D,$00
.byte $00,$4A,$4B,$65,$7D,$7E,$7F,$80,$66,$67,$65,$78,$00,$00,$00,$68
.byte $65,$65,$65,$65,$05,$06,$7F,$80,$66,$67,$7C,$78,$00,$00,$00,$65
.byte $07,$08,$0B,$0E,$0C,$0C,$0E,$0A,$00,$00,$00,$00,$00,$00,$55,$56
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$55,$56
.byte $00,$00,$00,$00,$00,$00,$00,$65,$3D,$00,$00,$55,$56,$00,$00,$00
.byte $00,$00,$00,$00,$55,$56,$00,$00,$00,$0A,$0B,$05,$06,$0A,$0F,$00
.byte $00,$00,$00,$15,$0B,$05,$06,$09,$0A,$07,$0B,$0E,$0C,$0C,$0E,$00
.byte $00,$00,$4A,$4B,$65,$7D,$7E,$7B,$7A,$7B,$7A,$65,$65,$65,$65,$65
.byte $05,$06,$65,$65,$68,$66,$67,$7B,$7A,$7B,$7A,$7D,$7E,$7F,$80,$65
.byte $05,$06,$67,$7D,$7E,$7F,$80,$7B,$7A,$7B,$7A,$7D,$7E,$7F,$80,$65
.byte $05,$06,$7E,$66,$67,$7F,$68,$7B,$7A,$7B,$7A,$66,$67,$7D,$7E,$65
.byte $05,$06,$65,$66,$67,$66,$67,$7B,$7A,$7B,$7A,$7D,$7E,$7F,$80,$7D
.byte $65,$4A,$4B,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$03,$04,$00,$00,$00,$00,$00,$00,$00,$00,$00,$55,$56
.byte $00,$00,$00,$00,$00,$55,$56,$55,$34,$35,$36,$37,$56,$55,$56,$00
.byte $00,$00,$00,$00,$55,$56,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00
.byte $00,$00,$03,$04,$00,$05,$06,$00,$00,$00,$00,$00,$03,$04,$00,$05
.byte $06,$00,$00,$00,$00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$03,$04,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00
.byte $00,$0B,$08,$07,$0B,$0E,$0C,$0C,$0E,$09,$0A,$09,$08,$00,$00,$00
.byte $00,$00,$00,$00,$00,$05,$06,$00,$05,$06,$00,$00,$00,$00,$50,$51
.byte $52,$53,$54,$53,$53,$54,$53,$57,$00,$00,$00,$00,$00,$00,$03,$04
.byte $00,$05,$06,$00,$00,$00,$00,$00,$00,$00,$55,$56,$00,$00,$55,$56
.byte $00,$00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$55,$56,$00
.byte $00,$4A,$4B,$65,$7D,$65,$65,$7E,$65,$7D,$7E,$7B,$7A,$7B,$7A,$65
.byte $65,$65,$65,$65,$05,$06,$7E,$66,$67,$7D,$7E,$7B,$7A,$7B,$7A,$66
.byte $2F,$30,$33,$0E,$32,$30,$0E,$32,$00,$00,$00,$00,$00,$00,$03,$04
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$03,$04
.byte $00,$00,$00,$00,$00,$00,$00,$55,$56,$00,$00,$05,$06,$00,$00,$00
.byte $00,$00,$00,$00,$03,$04,$00,$00,$00,$32,$33,$03,$04,$32,$14,$00
.byte $00,$00,$00,$16,$33,$03,$04,$31,$2F,$30,$33,$0E,$32,$30,$0E,$00
.byte $00,$00,$4A,$4B,$27,$4E,$52,$54,$4E,$52,$4E,$54,$4E,$4E,$52,$52
.byte $4E,$4E,$52,$54,$4E,$52,$4E,$54,$4E,$4E,$52,$52,$4E,$4E,$52,$54
.byte $4E,$52,$4E,$54,$4E,$4E,$52,$52,$4E,$4E,$52,$54,$4E,$52,$4E,$54
.byte $4E,$4E,$52,$52,$52,$4E,$54,$4E,$4E,$52,$4E,$54,$4E,$4E,$52,$52
.byte $4E,$4E,$52,$54,$4E,$52,$4E,$4E,$52,$54,$4E,$52,$4E,$54,$4E,$4E
.byte $52,$52,$4E,$51,$52,$53,$51,$52,$53,$51,$52,$53,$57,$00,$00,$00
.byte $00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$00,$00,$00,$00,$03,$04
.byte $00,$00,$00,$00,$00,$03,$04,$03,$5C,$5C,$5C,$5F,$04,$05,$06,$00
.byte $00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00
.byte $00,$00,$01,$02,$00,$03,$04,$00,$00,$00,$00,$00,$01,$02,$00,$03
.byte $04,$00,$00,$00,$00,$00,$00,$00,$50,$52,$53,$54,$51,$57,$00,$00
.byte $00,$00,$00,$00,$00,$00,$05,$06,$00,$00,$00,$00,$00,$00,$00,$00
.byte $50,$52,$53,$54,$52,$52,$53,$54,$51,$53,$53,$54,$57,$00,$00,$00
.byte $00,$33,$2D,$2E,$33,$0E,$32,$2F,$0E,$31,$32,$31,$30,$00,$00,$00
.byte $00,$00,$00,$00,$00,$01,$02,$00,$03,$04,$00,$00,$00,$00,$00,$65
.byte $3D,$00,$55,$56,$00,$65,$3D,$00,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$03,$04,$00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$00,$00,$00,$05,$06,$00,$50,$51,$52,$53,$54
.byte $53,$54,$52,$52,$4E,$52,$54,$4E,$52,$54,$53,$54,$4E,$52,$4E,$52
.byte $54,$4E,$52,$52,$4E,$4E,$52,$52,$4E,$4E,$52,$54,$4E,$52,$4E,$54
.byte $0B,$07,$09,$0E,$0C,$0C,$0E,$09,$09,$00,$00,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$03,$04,$00,$00,$00
.byte $00,$00,$00,$00,$01,$02,$00,$00,$00,$07,$0B,$01,$02,$07,$0F,$00
.byte $00,$00,$00,$15,$0B,$01,$02,$09,$0A,$07,$0B,$0E,$0C,$0C,$0E,$00
.byte $00,$00,$4C,$4D,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $65,$4C,$4D,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00,$00,$00,$00
.byte $50,$51,$52,$53,$51,$52,$53,$57,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$01,$02,$05,$5D,$5E,$5C,$5F,$06,$03,$04,$00
.byte $00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$05,$06,$00,$00,$00
.byte $00,$00,$05,$06,$00,$01,$02,$00,$00,$00,$00,$00,$05,$06,$00,$01
.byte $02,$00,$00,$00,$00,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00,$00
.byte $00,$00,$50,$51,$52,$52,$53,$54,$51,$53,$54,$57,$00,$00,$00,$00
.byte $00,$00,$55,$56,$00,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00,$00
.byte $09,$08,$0B,$0A,$09,$0E,$0C,$0C,$0E,$0A,$07,$08,$09,$0A,$00,$00
.byte $00,$00,$00,$00,$00,$05,$06,$00,$01,$02,$00,$00,$00,$00,$00,$55
.byte $56,$00,$01,$02,$00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$05,$06
.byte $00,$01,$02,$00,$00,$00,$00,$00,$00,$00,$05,$06,$00,$00,$05,$06
.byte $00,$00,$00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$65,$3D,$00
.byte $00,$4C,$4D,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $65,$65,$65,$65,$55,$56,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65
.byte $33,$2F,$31,$0E,$30,$31,$0E,$31,$31,$00,$00,$00,$50,$52,$53,$54
.byte $51,$57,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$50,$52,$53,$54
.byte $51,$57,$00,$00,$00,$00,$00,$03,$04,$00,$00,$01,$02,$00,$00,$00
.byte $00,$00,$00,$00,$03,$04,$00,$00,$00,$2F,$33,$01,$02,$2F,$14,$00
.byte $00,$00,$00,$16,$33,$01,$02,$31,$32,$2F,$33,$0E,$30,$31,$0E,$00
.byte $00,$00,$23,$24,$65,$65,$65,$65,$87,$88,$89,$8A,$65,$65,$65,$65
.byte $01,$02,$7D,$68,$7F,$80,$65,$65,$65,$65,$7F,$80,$65,$65,$65,$65
.byte $01,$02,$65,$65,$65,$7D,$68,$79,$00,$00,$00,$38,$65,$65,$68,$65
.byte $01,$02,$65,$65,$65,$65,$65,$65,$65,$65,$65,$65,$68,$66,$67,$65
.byte $01,$02,$66,$68,$65,$65,$65,$65,$65,$68,$65,$65,$65,$65,$7D,$7E
.byte $7C,$23,$24,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$50,$52,$53,$54
.byte $51,$57,$00,$00,$00,$03,$04,$03,$5C,$5C,$5C,$5F,$04,$01,$02,$00
.byte $00,$00,$50,$52,$53,$54,$51,$52,$53,$51,$52,$53,$52,$53,$51,$52
.byte $53,$54,$51,$52,$53,$51,$52,$53,$51,$52,$51,$52,$52,$51,$52,$53
.byte $51,$52,$57,$00,$00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$03,$04,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00
.byte $31,$30,$33,$2D,$2E,$0E,$2F,$31,$0E,$32,$2F,$30,$31,$32,$00,$00
.byte $00,$00,$00,$50,$54,$51,$52,$53,$51,$52,$53,$51,$52,$51,$52,$52
.byte $51,$52,$53,$51,$52,$53,$51,$52,$52,$51,$52,$53,$51,$52,$52,$51
.byte $52,$53,$51,$52,$57,$00,$00,$00,$00,$00,$03,$04,$00,$00,$03,$04
.byte $00,$00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$55,$56,$00
.byte $00,$23,$24,$65,$87,$88,$89,$8A,$65,$65,$65,$65,$65,$65,$65,$7D
.byte $68,$66,$67,$65,$01,$02,$65,$65,$65,$7F,$80,$66,$65,$65,$65,$65
.byte $07,$0B,$07,$0E,$0C,$0C,$0E,$0A,$07,$08,$00,$00,$00,$00,$55,$56
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$55,$56
.byte $00,$00,$00,$00,$00,$00,$00,$05,$06,$00,$00,$03,$04,$00,$00,$00
.byte $00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$0B,$03,$04,$0F,$00,$00
.byte $00,$00,$00,$00,$15,$03,$04,$09,$0A,$07,$0B,$0E,$0C,$0C,$0E,$0A
.byte $07,$00,$25,$26,$68,$66,$67,$65,$8B,$65,$65,$8C,$80,$65,$65,$65
.byte $03,$04,$7E,$66,$67,$7F,$80,$65,$68,$7D,$7E,$7F,$80,$7D,$65,$65
.byte $03,$04,$65,$65,$66,$67,$7C,$78,$00,$00,$00,$38,$65,$68,$66,$65
.byte $03,$04,$66,$68,$65,$65,$66,$67,$7D,$65,$65,$68,$66,$67,$7F,$65
.byte $03,$04,$65,$66,$67,$66,$68,$65,$68,$7F,$80,$65,$65,$65,$65,$7F
.byte $80,$25,$26,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$00,$00,$00,$00,$55,$56
.byte $00,$00,$00,$00,$00,$05,$06,$01,$5C,$5C,$5C,$5F,$02,$03,$04,$00
.byte $00,$00,$00,$00,$55,$56,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00
.byte $00,$00,$65,$3D,$00,$55,$56,$00,$00,$00,$00,$00,$65,$3D,$00,$55
.byte $56,$00,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00
.byte $00,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$00,$00,$00,$00
.byte $00,$00,$01,$02,$00,$00,$00,$00,$00,$00,$05,$04,$00,$00,$00,$08
.byte $07,$07,$08,$0B,$0A,$0E,$0C,$0C,$0E,$08,$0A,$07,$08,$09,$0A,$00
.byte $00,$00,$00,$00,$00,$55,$56,$00,$65,$3D,$00,$00,$00,$00,$00,$65
.byte $3D,$00,$55,$56,$00,$65,$3D,$00,$00,$00,$00,$00,$00,$00,$65,$3D
.byte $00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$00,$50,$51,$52,$53,$54,$57,$00,$05,$06,$00
.byte $00,$25,$26,$68,$8B,$65,$65,$8C,$67,$66,$67,$65,$65,$65,$65,$65
.byte $7D,$7E,$66,$68,$03,$04,$65,$68,$7D,$7E,$66,$67,$7D,$65,$65,$68
.byte $2F,$33,$2F,$0E,$33,$32,$0E,$32,$2F,$30,$00,$00,$00,$00,$03,$04
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$03,$04
.byte $00,$00,$00,$00,$50,$52,$51,$52,$53,$51,$52,$53,$51,$52,$53,$57
.byte $00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$2E,$01,$02,$14,$00,$00
.byte $00,$00,$00,$00,$16,$01,$02,$31,$2F,$30,$33,$0E,$33,$32,$0E,$32
.byte $2F,$00,$4A,$4B,$66,$67,$7F,$80,$8B,$65,$65,$8C,$7F,$80,$66,$65
.byte $01,$02,$7F,$80,$66,$27,$52,$4E,$52,$54,$4E,$52,$4F,$80,$7D,$65
.byte $01,$02,$65,$66,$67,$7D,$7E,$79,$00,$00,$00,$38,$68,$7F,$80,$65
.byte $01,$02,$67,$66,$67,$27,$52,$4E,$52,$54,$4E,$52,$4F,$7D,$7E,$65
.byte $01,$02,$65,$65,$7F,$80,$65,$65,$7D,$7E,$7F,$80,$66,$68,$65,$65
.byte $66,$4A,$4B,$00,$00,$00,$00,$00,$05,$06,$00,$00,$00,$00,$00,$00
.byte $00,$50,$52,$53,$54,$51,$57,$00,$00,$00,$00,$00,$00,$00,$03,$04
.byte $00,$00,$00,$00,$50,$52,$51,$52,$53,$51,$52,$53,$51,$52,$53,$57
.byte $00,$00,$00,$00,$03,$04,$00,$00,$00,$00,$00,$03,$04,$00,$00,$00
.byte $00,$00,$55,$56,$00,$01,$02,$00,$00,$00,$00,$00,$55,$56,$00,$05
.byte $06,$00,$00,$00,$00,$00,$00,$50,$52,$51,$52,$53,$51,$52,$53,$51
.byte $52,$53,$52,$51,$52,$53,$51,$52,$53,$51,$52,$53,$52,$51,$52,$53
.byte $51,$52,$53,$51,$52,$53,$57,$00,$00,$00,$01,$02,$00,$00,$00,$30
.byte $2F,$2F,$30,$33,$2D,$0E,$33,$2F,$0E,$30,$32,$2F,$30,$31,$32,$00
.byte $00,$00,$00,$00,$00,$01,$02,$00,$55,$56,$00,$00,$00,$00,$00,$55
.byte $56,$00,$05,$06,$00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$55,$56
.byte $00,$05,$06,$00,$00,$00,$00,$50,$53,$51,$52,$53,$54,$53,$53,$54
.byte $53,$54,$57,$00,$00,$00,$00,$00,$55,$56,$00,$00,$00,$03,$04,$00
.byte $00,$4A,$4B,$66,$8B,$65,$65,$8C,$7F,$67,$7F,$80,$65,$65,$65,$65
.byte $68,$66,$67,$66,$01,$02,$65,$65,$65,$27,$52,$4E,$52,$54,$4E,$52
.byte $09,$07,$0B,$0E,$0C,$0C,$0E,$09,$07,$08,$09,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$55,$56,$55,$56,$55,$56,$55,$56,$55,$56,$00
.byte $00,$00,$0A,$0B,$03,$04,$0A,$00,$00,$09,$0B,$01,$02,$0F,$00,$00
.byte $00,$00,$00,$00,$15,$01,$02,$09,$0A,$07,$0B,$0E,$0C,$0C,$0E,$09
.byte $0A,$07,$4A,$4B,$65,$66,$68,$66,$8B,$65,$65,$8C,$7E,$66,$67,$65
.byte $01,$02,$7E,$7D,$65,$65,$55,$56,$55,$56,$55,$56,$68,$66,$67,$65
.byte $01,$02,$7F,$80,$7F,$80,$7C,$78,$00,$00,$00,$38,$7F,$80,$66,$65
.byte $01,$02,$7D,$7E,$65,$65,$55,$56,$55,$56,$55,$56,$7D,$7E,$66,$65
.byte $01,$02,$65,$65,$65,$65,$65,$7D,$7E,$7D,$7E,$66,$67,$65,$65,$65
.byte $68,$4A,$4B,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$00
.byte $00,$00,$00,$55,$56,$00,$00,$00,$00,$00,$00,$00,$00,$00,$01,$02
.byte $00,$00,$00,$00,$00,$55,$56,$55,$56,$55,$56,$55,$56,$55,$56,$00
.byte $00,$00,$00,$00,$01,$02,$00,$00,$00,$00,$00,$05,$06,$00,$00,$00
.byte $07,$08,$09,$0A,$0B,$03,$04,$09,$08,$07,$08,$09,$0A,$09,$0B,$03
.byte $04,$08,$0A,$00,$00,$00,$00,$00,$55,$56,$55,$56,$55,$56,$00,$00
.byte $00,$00,$00,$00,$55,$56,$55,$56,$55,$56,$00,$00,$00,$00,$00,$00
.byte $55,$56,$55,$56,$55,$56,$00,$00,$00,$00,$03,$04,$00,$00,$07,$08
.byte $09,$0A,$09,$08,$0B,$0E,$0C,$0C,$0E,$08,$09,$0A,$09,$08,$07,$0A
.byte $00,$00,$00,$07,$0B,$03,$04,$09,$0A,$09,$09,$08,$07,$08,$09,$0A
.byte $09,$0B,$03,$04,$08,$0A,$09,$09,$08,$07,$08,$09,$08,$09,$0A,$09
.byte $0B,$03,$04,$08,$09,$00,$00,$00,$00,$00,$55,$56,$55,$56,$55,$56
.byte $00,$00,$00,$00,$00,$00,$00,$00,$01,$02,$00,$00,$00,$01,$02,$00
.byte $00,$4A,$4B,$65,$8B,$65,$65,$8C,$67,$66,$68,$66,$68,$7D,$65,$65
.byte $66,$67,$65,$65,$01,$02,$65,$65,$65,$65,$55,$56,$55,$56,$55,$56
.byte $31,$2F,$33,$0E,$32,$33,$0E,$31,$2F,$30,$31,$00,$29,$2A,$05,$06
.byte $2B,$2C,$00,$00,$00,$00,$00,$00,$00,$00,$00,$00,$29,$2A,$05,$06
.byte $2B,$2C,$00,$29,$2A,$01,$02,$03,$04,$05,$06,$01,$02,$03,$04,$2B
.byte $2C,$00,$32,$33,$05,$06,$32,$00,$00,$31,$33,$03,$04,$14,$00,$00
.byte $00,$00,$00,$00,$16,$03,$04,$31,$32,$2F,$33,$0E,$32,$33,$0E,$31
.byte $32,$2F,$4A,$4B,$65,$65,$7D,$7E,$8B,$65,$65,$8C,$7D,$7E,$7F,$65
.byte $03,$04,$65,$65,$65,$65,$01,$02,$03,$04,$05,$06,$65,$68,$65,$65
.byte $03,$04,$7E,$7D,$7E,$66,$67,$79,$00,$00,$00,$38,$68,$66,$67,$65
.byte $03,$04,$65,$7D,$65,$65,$01,$02,$03,$04,$05,$06,$67,$7F,$80,$65
.byte $03,$04,$65,$65,$7D,$7E,$7F,$80,$66,$67,$7D,$7E,$66,$67,$65,$65
.byte $65,$4A,$4B,$00,$00,$00,$29,$2A,$05,$06,$2B,$2C,$00,$00,$00,$00
.byte $00,$29,$2A,$05,$06,$2B,$2C,$00,$00,$00,$00,$00,$29,$2A,$05,$06
.byte $2B,$2C,$00,$29,$2A,$01,$02,$03,$04,$05,$06,$01,$02,$03,$04,$2B
.byte $2C,$00,$29,$2A,$05,$06,$2B,$2C,$00,$29,$2A,$05,$06,$2B,$2C,$00
.byte $2F,$30,$31,$32,$33,$05,$06,$31,$30,$2F,$30,$31,$32,$31,$33,$01
.byte $02,$30,$32,$00,$00,$00,$29,$2A,$01,$02,$03,$04,$05,$06,$2B,$2C
.byte $00,$00,$29,$2A,$03,$04,$05,$06,$01,$02,$2B,$2C,$00,$00,$29,$2A
.byte $05,$06,$01,$02,$03,$04,$2B,$2C,$29,$2A,$01,$02,$2B,$2C,$2F,$30
.byte $31,$32,$31,$30,$33,$0E,$2D,$33,$0E,$30,$31,$32,$31,$30,$2F,$32
.byte $00,$00,$00,$2F,$33,$05,$06,$31,$32,$31,$31,$30,$2F,$30,$31,$32
.byte $31,$33,$01,$02,$30,$32,$31,$31,$30,$2F,$30,$31,$30,$31,$32,$31
.byte $33,$01,$02,$30,$31,$00,$00,$00,$29,$2A,$05,$06,$01,$02,$03,$04
.byte $2B,$2C,$00,$00,$00,$00,$00,$00,$05,$06,$00,$00,$00,$01,$02,$00
.byte $00,$4A,$4B,$65,$8B,$65,$65,$8C,$7F,$65,$7D,$7E,$7F,$80,$65,$66
.byte $68,$65,$65,$65,$03,$04,$65,$65,$65,$65,$01,$02,$03,$04,$05,$06
.byte $6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B
.byte $6C,$6D,$6E,$6F,$71,$84,$85,$86,$84,$85,$69,$70,$6A,$6B,$6C,$6D
.byte $6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F
.byte $70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A
.byte $6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C
.byte $6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E
.byte $6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70
.byte $6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B
.byte $6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D
.byte $6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F
.byte $70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A
.byte $6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C
.byte $6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E
.byte $6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70
.byte $6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B
.byte $6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D
.byte $6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F
.byte $70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A
.byte $6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C
.byte $6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E
.byte $6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70
.byte $6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B
.byte $6C,$6D,$71,$84,$85,$86,$84,$69,$6F,$70,$6C,$6D,$6A,$6B,$6C,$6D
.byte $6E,$6F,$70,$6A,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E
.byte $6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70,$6A,$6B,$6C,$6D,$6E,$6F,$70
.byte $73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74
.byte $75,$76,$77,$81,$83,$73,$74,$75,$75,$74,$72,$82,$73,$74,$75,$76
.byte $77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81
.byte $82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73
.byte $74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75
.byte $76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77
.byte $81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82
.byte $73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74
.byte $75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76
.byte $77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81
.byte $82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73
.byte $74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75
.byte $76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77
.byte $81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82
.byte $73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74
.byte $75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76
.byte $77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81
.byte $82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73
.byte $74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75
.byte $76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77
.byte $81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82
.byte $73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74
.byte $75,$76,$83,$73,$74,$75,$75,$72,$81,$82,$75,$76,$73,$74,$75,$76
.byte $77,$81,$82,$73,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77
.byte $81,$82,$73,$74,$75,$76,$77,$81,$82,$73,$74,$75,$76,$77,$81,$82
